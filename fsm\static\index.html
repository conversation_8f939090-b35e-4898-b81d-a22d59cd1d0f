<!DOCTYPE html>
<html>
<head>
    <title><PERSON><PERSON><PERSON><PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .section-box {
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            background-color: white;
        }
        .section-box h3 {
            margin: 0 0 10px 0;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status p {
            margin: 5px 0;
            padding: 5px;
            border-bottom: 1px solid #eee;
        }
        .error {
            color: #d32f2f;
            background-color: #ffebee;
            padding: 5px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .success {
            color: #388e3c;
            background-color: #e8f5e9;
            padding: 5px;
            margin: 5px 0;
            border-radius: 4px;
        }
        .warning {
            color: #f57c00;
            background-color: #fff3e0;
            padding: 5px;
            margin: 5px 0;
            border-radius: 4px;
        }
        button {
            background-color: #1976d2;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #1565c0;
        }
        button:disabled {
            background-color: #bdbdbd;
            cursor: not-allowed;
        }
        .section-box.pending {
            background-color: #fafafa;
        }
        .section-box.in_queue {
            background-color: #e3f2fd;
        }
        .section-box.processing {
            background-color: #fff3e0;
        }
        .section-box.waiting_open {
            background-color: #e8f5e9;
            animation: pulse 2s infinite;
        }
        .section-box.success {
            background-color: #e8f5e9;
        }
        .section-box.error {
            background-color: #ffebee;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        #pinScreen, #sectionScreen {
            display: none;
        }
        .pin-input {
            font-size: 24px;
            padding: 10px;
            width: 200px;
            text-align: center;
            margin: 20px 0;
        }
        .section-info {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .led-info {
            color: #1976d2;
            font-style: italic;
        }
        .user-instruction {
            background-color: #e3f2fd;
            border-left: 4px solid #1976d2;
            padding: 10px 15px;
            margin: 10px 0;
            font-size: 16px;
            border-radius: 4px;
        }
        .user-instruction.important {
            background-color: #fff3e0;
            border-left-color: #f57c00;
        }
    </style>
</head>
<body>
    <h1>Testování Sekvence Sekcí</h1>
    
    <!-- PIN obrazovka -->
    <div id="pinScreen" class="container">
        <h2>Zadejte PIN</h2>
        <div class="user-instruction">
            Pro odemčení schránek zadejte váš 6místný PIN kód.
        </div>
        <div class="input-group">
            <input type="password" id="pinInput" class="pin-input" maxlength="6" placeholder="123456">
        </div>
        <button id="validatePin">Ověřit PIN</button>
        <div id="pinError" class="error"></div>
    </div>

    <!-- Obrazovka sekcí -->
    <div id="sectionScreen" class="container">
        <h2>Sekce k otevření</h2>
        <div class="user-instruction important">
            Postupujte podle instrukcí u každé sekce. U temperovaných schránek je nutné:
            1. Počkat na odemčení
            2. Otevřít dvířka
            3. Vyzvednout/vložit zásilku
            4. Zavřít dvířka
        </div>
        <div id="sectionGrid" class="section-grid">
            <!-- Sekce budou přidány dynamicky -->
        </div>
        <div class="status" id="status"></div>
    </div>

    <script>
        class SectionSequenceClient {
            constructor() {
                this.ws = null;
                this.sessionId = null;
                this.sections = [];
                
                // UI elements
                this.pinScreen = document.getElementById('pinScreen');
                this.sectionScreen = document.getElementById('sectionScreen');
                this.pinInput = document.getElementById('pinInput');
                this.validatePinBtn = document.getElementById('validatePin');
                this.pinError = document.getElementById('pinError');
                this.sectionGrid = document.getElementById('sectionGrid');
                this.status = document.getElementById('status');
                
                // Bind event listeners
                this.validatePinBtn.onclick = () => this.validatePin();
                
                // Show PIN screen
                this.pinScreen.style.display = 'block';
            }
            
            addStatus(message, type = 'info') {
                const p = document.createElement('p');
                const time = new Date().toLocaleTimeString();
                p.textContent = `${time}: ${message}`;
                
                if (type === 'error') p.classList.add('error');
                else if (type === 'success') p.classList.add('success');
                else if (type === 'warning') p.classList.add('warning');
                
                this.status.insertBefore(p, this.status.firstChild);
            }
            
            getInstructionForStatus(status, isTempered) {
                switch(status) {
                    case 'pending':
                        return 'Čekejte prosím, sekce se připravuje...';
                    case 'in_queue':
                        return 'Sekce čeká na zpracování...';
                    case 'processing':
                        return isTempered 
                            ? 'Probíhá odemykání, čekejte prosím...'
                            : 'Odemykání schránky...';
                    case 'waiting_open':
                        return 'Nyní můžete otevřít dvířka schránky';
                    case 'success':
                        return isTempered 
                            ? 'Schránka je odemčena. Otevřete dvířka, vyzvedněte/vložte zásilku a zavřete dvířka.'
                            : 'Schránka je odemčena. Můžete ji otevřít.';
                    case 'error':
                        return 'Došlo k chybě. Kontaktujte prosím obsluhu.';
                    default:
                        return 'Čekejte prosím...';
                }
            }

            updateSectionStatus(sectionId, status, message = '') {
                const sectionBox = document.getElementById(`section-${sectionId}`);
                if (sectionBox) {
                    // Najdeme konfiguraci sekce
                    const section = this.sections.find(s => s.section_id === sectionId);
                    const isTempered = section ? section.is_tempered : false;

                    // Odstraníme všechny stavové třídy
                    sectionBox.classList.remove('pending', 'in_queue', 'processing', 'waiting_open', 'success', 'error');
                    // Přidáme novou třídu podle stavu
                    sectionBox.classList.add(status);
                    
                    // Aktualizujeme text stavu a instrukce
                    const statusText = sectionBox.querySelector('.status-text');
                    const instructionText = sectionBox.querySelector('.user-instruction');
                    
                    if (message) {
                        // Pokud máme specifickou zprávu, použijeme ji jako instrukci
                        if (instructionText) instructionText.textContent = message;
                        if (statusText) statusText.textContent = this.getInstructionForStatus(status, isTempered);
                    } else {
                        // Jinak použijeme standardní texty
                        if (statusText) statusText.textContent = this.getInstructionForStatus(status, isTempered);
                        if (instructionText) instructionText.textContent = this.getInstructionForStatus(status, isTempered);
                    }
                }
            }
            
            async validatePin() {
                try {
                    const pin = this.pinInput.value;
                    if (!pin) {
                        this.pinError.textContent = 'Zadejte PIN';
                        return;
                    }
                    
                    const response = await fetch('/api/validate-pin', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ pin })
                    });
                    
                    if (!response.ok) {
                        const error = await response.json();
                        throw new Error(error.detail || 'Neplatný PIN');
                    }
                    
                    const result = await response.json();
                    this.sessionId = result.session_id;
                    this.sections = result.sections;
                    
                    // Přepneme na obrazovku sekcí
                    this.pinScreen.style.display = 'none';
                    this.sectionScreen.style.display = 'block';
                    
                    // Vytvoříme grid sekcí
                    this.sectionGrid.innerHTML = this.sections.map(section => `
                        <div id="section-${section.section_id}" class="section-box pending">
                            <h3>Sekce ${section.section_id}</h3>
                            <div class="section-info">
                                ${section.is_tempered ? 'Temperovaný zámek' : 'Standardní zámek'}
                                ${section.led_section ? `<div class="led-info">LED sekce: ${section.led_section}</div>` : ''}
                            </div>
                            <div class="status-text">Čeká na start</div>
                            <div class="user-instruction">
                                ${this.getInstructionForStatus('pending', section.is_tempered)}
                            </div>
                        </div>
                    `).join('');
                    
                    // Připojíme WebSocket
                    this.connectWebSocket();
                    
                } catch (error) {
                    this.pinError.textContent = error.message;
                }
            }
            
            connectWebSocket() {
                const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${wsProtocol}//${window.location.host}/ws/${this.sessionId}`;
                
                this.ws = new WebSocket(wsUrl);
                
                this.ws.onopen = () => {
                    this.addStatus('WebSocket připojen');
                    // Pošleme ready_for_sequence
                    this.ws.send(JSON.stringify({
                        type: 'ready_for_sequence'
                    }));
                };
                
                this.ws.onmessage = (event) => {
                    const message = JSON.parse(event.data);
                    
                    switch (message.type) {
                        case 'init':
                            this.addStatus('Inicializace sekvence');
                            message.sections.forEach(section => {
                                this.updateSectionStatus(section.id, section.status);
                            });
                            break;
                            
                        case 'sequence_starting':
                            this.addStatus('Spouštění sekvence');
                            message.sections.forEach(section => {
                                this.updateSectionStatus(section.id, section.status);
                            });
                            break;
                            
                        case 'section_update':
                            this.updateSectionStatus(
                                message.section_id,
                                message.status,
                                message.message
                            );
                            this.addStatus(
                                `Sekce ${message.section_id}: ${message.message}`,
                                message.status === 'error' ? 'error' : 'info'
                            );
                            break;
                            
                        case 'sequence_complete':
                            const successRate = `${message.success_count}/${message.total_count}`;
                            this.addStatus(
                                `Sekvence dokončena (${successRate} úspěšných)`,
                                message.success_count === message.total_count ? 'success' : 'warning'
                            );
                            break;
                            
                        case 'error':
                            this.addStatus(message.message, 'error');
                            break;
                    }
                };
                
                this.ws.onclose = () => {
                    this.addStatus('WebSocket odpojen', 'warning');
                };
                
                this.ws.onerror = (error) => {
                    this.addStatus('WebSocket chyba: ' + error.message, 'error');
                };
            }
        }
        
        // Start the application
        const app = new SectionSequenceClient();
    </script>
</body>
</html> 