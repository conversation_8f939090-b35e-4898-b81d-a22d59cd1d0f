import asyncio
from managers.electronics_api import send_command
from managers.ws_manager import ws_manager
from managers.error_manager import error_manager, SectionError, ErrorType, ErrorCode
import logging
from typing import Dict, Optional

logger = logging.getLogger(__name__)

class CommandType:
    UNLOCK = "unlock"
    CHECK_DOOR = "check_door"
    LOCK = "lock"
    ENABLE_LED = "enable_led"
    DISABLE_LED = "disable_led"
    WRITE_LED_COLORS = "write_led_colors"

class LEDColor:
    YELLOW = "A"  # žlutá - schránka je v pořadí
    GREEN = "B"   # zelená - tuto má zákazník otevřít
    RED = "C"     # červená - zatím neimplementováno

class LockerManager:
    def __init__(self):
        self.tasks = {}
        self.MAX_RETRIES = 2
        self.RETRY_DELAY = 2  
        self.MAX_CONSECUTIVE_ERRORS = 3
        # Stateful LED systém - sledujeme stav všech LED
        self.led_states = {}  # {led_id: color}

    async def _execute_command(
        self,
        section_id: str,
        command_type: str,
        command: str,
        no_retry: bool = False,  # pokud True, vypne retry v electronics_api
        is_tempered: bool = True  # True pro temperovaný zámek
    ) -> Dict:
        """
        Spustí command s využitím retry logiky z electronics_api
        """
        try:
            result = await send_command(section_id, command, max_retries=(1 if no_retry else 2), is_tempered=is_tempered)
            
            if result.startswith('-'):
                error_message = "Timeout při komunikaci" if result == "-12" else f"Chyba (kód: {result})"
                logger.error(f"{command_type} failed for section {section_id}: {error_message}")
                return {
                    "success": False,
                    "error": error_message,
                    "error_code": result
                }
                
            return {
                "success": True,
                "result": result
            }
            
        except Exception as e:
            logger.error(f"Exception in {command_type} for section {section_id}: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "error_code": "exception"
            }

    async def start_unlock_sequence(self, section_id: str, is_tempered: bool = True, session_id: Optional[str] = None, led_section: Optional[int] = None):
        """
        Zahájí sekvenci odemykání sekce
        
        Args:
            section_id: ID sekce k odemčení
            is_tempered: True pro temperovaný zámek, False pro netemperovaný
            session_id: ID WebSocket session pro posílání zpráv
            led_section: ID LED sekce pro ovládání LED
        """
        if section_id in self.tasks:
            raise SectionError(
                message="Sekce se již zpracovává",
                error_type=ErrorType.VALIDATION,
                error_code=ErrorCode.INVALID_SECTION,
                section_id=section_id
            )
        
        # Používáme session_id pro WebSocket zprávy, section_id pro hardware
        ws_id = session_id or section_id
        
        # Pokus o unlock
        unlock_result = await self._execute_command(
            section_id, 
            CommandType.UNLOCK, 
            "unlock",
            is_tempered=is_tempered
        )
        
        if not unlock_result["success"]:
            await ws_manager.send(ws_id, {
                "type": "section_update",
                "section_id": section_id,
                "status": "error",
                "message": f"Chyba při odemykání schránky č. {section_id}: {unlock_result['error']}"
            })
            return {
                "success": False,
                "unlock_success": False,
                "error": unlock_result["error"]
            }

        # Oznámíme úspěšné odemčení
        await ws_manager.send(ws_id, {
            "type": "section_update",
            "section_id": section_id,
            "status": "unlocked",
            "message": f"Schránka č. {section_id} byla úspěšně odemčena"
        })
        
        # Nastavíme LED na zelenou (B) - tuto má zákazník otevřít
        if led_section is not None:
            await self.set_led_color(str(led_section), LEDColor.GREEN, ws_id)
        
        # Yield control for immediate message flush
        await asyncio.sleep(0)
            
        # Spustíme background task pro čekání na dveře (nyní i pro netemperované)
        self.tasks[section_id] = asyncio.create_task(
            self._monitor_door_sequence(section_id, ws_id, led_section, is_tempered)
        )
        
        return {
            "success": True,
            "unlock_success": True,
            "error": None
        }
    
    async def _monitor_door_sequence(self, section_id: str, ws_id: str, led_section: Optional[int] = None, is_tempered: bool = True):
        """
        Monitoruje sekvenci otevření a zavření dveří v background tasku
        """
        try:
            # Čekáme na otevření dveří
            await self._wait_for_door_open_nonblocking(section_id, ws_id, led_section, is_tempered)
            
            # Čekáme na zavření dveří
            await self._wait_for_door_close_nonblocking(section_id, ws_id, led_section, is_tempered)
            
        except Exception as e:
            await ws_manager.send(ws_id, {
                "type": "section_update", 
                "section_id": section_id,
                "status": "error",
                "message": f"Chyba při monitorování dveří: {str(e)}"
            })
        finally:
            # Vyčistíme task
            if section_id in self.tasks:
                del self.tasks[section_id]
    
    async def _wait_for_door_open_nonblocking(self, section_id: str, ws_id: str, led_section: Optional[int] = None, is_tempered: bool = True):
        """
        Čeká na otevření dveří s okamžitým odesíláním WebSocket zpráv
        Timeout: 60 sekund, pak pokračuje sekvence dál
        """
        import time
        start_time = time.time()
        timeout_seconds = 60
        first_check = True
        
        while True:
            # Kontrola timeout
            elapsed_time = time.time() - start_time
            if elapsed_time >= timeout_seconds:
                await ws_manager.send(ws_id, {
                    "type": "section_update",
                    "section_id": section_id,
                    "status": "timeout_open",
                    "message": f"Schránka č. {section_id} - timeout při čekání na otevření (60s) - pokračujeme v sekvenci"
                })
                
                # Reset LED zpátky na žlutou (A) i při timeout
                if led_section is not None:
                    await self.set_led_state(led_section, LEDColor.YELLOW)
                    await self.update_all_leds(ws_id)
                
                return True  # Pokračujeme v sekvenci
            
            check_result = await self._execute_command(
                section_id, 
                CommandType.CHECK_DOOR, 
                "check_door",
                no_retry=False,
                is_tempered=is_tempered
            )
            
            if not check_result["success"]:
                await ws_manager.send(ws_id, {
                    "type": "section_update",
                    "section_id": section_id,
                    "status": "error",
                    "message": f"Chyba při kontrole stavu dveří schránky č. {section_id}: {check_result['error']}"
                })
                return False

            state = check_result["result"]
            
            # Dveře jsou zavřené, čekáme na otevření
            if state == "1 0":
                remaining_time = timeout_seconds - elapsed_time
                if first_check:
                    await ws_manager.send(ws_id, {
                        "type": "section_update",
                        "section_id": section_id,
                        "status": "waiting_open",
                        "message": f"Schránka č. {section_id} byla odemčena - nyní můžete otevřít dvířka (zbývá {int(remaining_time)}s)"
                    })
                    first_check = False
                else:
                    await ws_manager.send(ws_id, {
                        "type": "section_update",
                        "section_id": section_id,
                        "status": "waiting_open",
                        "message": f"Schránka č. {section_id} je stále zavřená - prosím otevřete schránku (zbývá {int(remaining_time)}s)"
                    })
            
            # Dveře byly otevřeny
            elif state == "1 1":
                await ws_manager.send(ws_id, {
                    "type": "section_update",
                    "section_id": section_id,
                    "status": "door_opened",
                    "message": f"Schránka č. {section_id} - zákazník otevřel schránku... prosím zavřete schránku"
                })
                
                # Hned po otevření zavoláme lock_tempered POUZE u temperovaných zámků
                if is_tempered:
                    lock_result = await self._execute_command(
                        section_id,
                        CommandType.LOCK,
                        "lock",
                        is_tempered=True
                    )
                    
                    if not lock_result["success"]:
                        await ws_manager.send(ws_id, {
                            "type": "section_update",
                            "section_id": section_id,
                            "status": "error",
                            "message": f"Chyba při zamykání schránky č. {section_id}: {lock_result['error']}"
                        })
                        return False
                
                break

            # Kratší interval pro rychlejší odezvu
            await asyncio.sleep(0.5)
        
        return True
    
    async def _wait_for_door_close_nonblocking(self, section_id: str, ws_id: str, led_section: Optional[int] = None, is_tempered: bool = True):
        """
        Čeká na zavření dveří s okamžitým odesíláním WebSocket zpráv
        Timeout: 30 sekund, pak pokračuje sekvence dál
        """
        import time
        start_time = time.time()
        timeout_seconds = 30
        
        while True:
            # Kontrola timeout
            elapsed_time = time.time() - start_time
            if elapsed_time >= timeout_seconds:
                await ws_manager.send(ws_id, {
                    "type": "section_update",
                    "section_id": section_id,
                    "status": "timeout_close",
                    "message": f"Schránka č. {section_id} - timeout při čekání na zavření (30s) - pokračujeme v sekvenci"
                })
                
                # Reset LED zpátky na žlutou (A) i při timeout
                if led_section is not None:
                    await self.set_led_state(led_section, LEDColor.YELLOW)
                    await self.update_all_leds(ws_id)
                
                return True  # Pokračujeme v sekvenci
            
            check_result = await self._execute_command(
                section_id, 
                CommandType.CHECK_DOOR, 
                "check_door",
                no_retry=False,
                is_tempered=is_tempered
            )
            
            if not check_result["success"]:
                await ws_manager.send(ws_id, {
                    "type": "section_update",
                    "section_id": section_id,
                    "status": "error",
                    "message": f"Chyba při kontrole stavu dveří schránky č. {section_id}: {check_result['error']}"
                })
                return False

            state = check_result["result"]
            
            # Dveře jsou stále otevřené
            if state == "1 1":
                remaining_time = timeout_seconds - elapsed_time
                await ws_manager.send(ws_id, {
                    "type": "section_update",
                    "section_id": section_id,
                    "status": "waiting_close",
                    "message": f"Schránka č. {section_id} - schránka je otevřená... prosím zavřete schránku (zbývá {int(remaining_time)}s)"
                })
            
            # Dveře byly zavřeny
            elif state == "1 0":
                await ws_manager.send(ws_id, {
                    "type": "section_update",
                    "section_id": section_id,
                    "status": "door_closed",
                    "message": f"Schránka č. {section_id} - zákazník zavřel schránku... konec sekvence"
                })
                
                # Reset LED zpátky na žlutou (A) - sekvence pro tuto schránku skončila
                if led_section is not None:
                    await self.set_led_state(led_section, LEDColor.YELLOW)
                    await self.update_all_leds(ws_id)
                
                break

            # Kratší interval pro rychlejší odezvu
            await asyncio.sleep(0.5)
        
        return True

    async def wait_for_sequence_completion(self, section_id: str, timeout: int = 300):
        """
        Čeká na dokončení background sekvence pro temperovaný zámek
        """
        if section_id not in self.tasks:
            return True  # Task již skončil nebo neexistuje
            
        try:
            await asyncio.wait_for(self.tasks[section_id], timeout=timeout)
            return True
        except asyncio.TimeoutError:
            # Timeout - zrušíme task
            if section_id in self.tasks:
                self.tasks[section_id].cancel()
                del self.tasks[section_id]
            return False
        except Exception:
            return False

    async def enable_leds(self, ws_id: Optional[str] = None):
        """
        Zapne LED systém
        """
        # Pro globální LED příkazy nepoužíváme _execute_command, ale send_command přímo
        try:
            result = await send_command("", "enable_led", max_retries=2, is_tempered=True)
            
            if result.startswith('-'):
                error_message = "Timeout při komunikaci" if result == "-12" else f"Chyba (kód: {result})"
                success = False
                error = error_message
            else:
                success = True
                error = None
        except Exception as e:
            success = False
            error = str(e)
        
        if not success and ws_id:
            await ws_manager.send(ws_id, {
                "type": "led_update",
                "status": "error",
                "message": f"Chyba při zapínání LED systému: {error}"
            })
        
        return success

    async def disable_leds(self, ws_id: Optional[str] = None):
        """
        Vypne LED systém a vyčistí stav
        """
        # Vyčistíme stav LED z paměti
        self.led_states.clear()
        
        # Pro globální LED příkazy nepoužíváme _execute_command, ale send_command přímo
        try:
            result = await send_command("", "disable_led", max_retries=2, is_tempered=True)
            
            if result.startswith('-'):
                error_message = "Timeout při komunikaci" if result == "-12" else f"Chyba (kód: {result})"
                success = False
                error = error_message
            else:
                success = True
                error = None
        except Exception as e:
            success = False
            error = str(e)
        
        if not success and ws_id:
            await ws_manager.send(ws_id, {
                "type": "led_update",
                "status": "error",
                "message": f"Chyba při vypínání LED systému: {error}"
            })
        
        return success

    async def set_led_state(self, led_section_id: int, color: str):
        """
        Nastaví stav LED v paměti (bez odeslání příkazu)
        """
        self.led_states[led_section_id] = color

    async def clear_led_state(self, led_section_id: int):
        """
        Vymaže LED stav z paměti
        """
        if led_section_id in self.led_states:
            del self.led_states[led_section_id]

    async def update_all_leds(self, ws_id: Optional[str] = None):
        """
        Pošle aktuální stav všech LED v jednom příkazu
        """
        if not self.led_states:
            return True  # Žádné LED k odeslání
            
        # Sestavíme příkaz: write_led_colors:1:A:2:B:3:A
        command_parts = ["write_led_colors"]
        for led_id, color in self.led_states.items():
            command_parts.extend([str(led_id), color])
        
        command = ":".join(command_parts)
        
        # Používáme send_command přímo pro LED příkazy
        try:
            result = await send_command("", command, max_retries=2, is_tempered=True)
            
            if result.startswith('-'):
                error_message = "Timeout při komunikaci" if result == "-12" else f"Chyba (kód: {result})"
                success = False
                error = error_message
            else:
                success = True
                error = None
        except Exception as e:
            success = False
            error = str(e)
        
        if not success and ws_id:
            await ws_manager.send(ws_id, {
                "type": "led_update",
                "status": "error",
                "message": f"Chyba při aktualizaci LED: {error}"
            })
        
        return success

    async def set_led_color(self, led_section_id: str, color: str, ws_id: Optional[str] = None):
        """
        Nastaví barvu LED pro konkrétní LED sekci a odešle všechny LED
        """
        await self.set_led_state(int(led_section_id), color)
        return await self.update_all_leds(ws_id)

    async def initialize_sequence_leds(self, sections: list, ws_id: Optional[str] = None):
        """
        Inicializuje LED pro celou sekvenci - zapne systém a nastaví všechny schránky na žlutou
        """
        # Vyčistíme stávající stav LED
        self.led_states.clear()
        
        # Zapneme LED systém
        if not await self.enable_leds(ws_id):
            return False
        
        # Nastavíme všechny schránky na žlutou (A) do paměti
        for section in sections:
            if section.led_section is not None:  # Jen pokud má schránka LED
                await self.set_led_state(section.led_section, LEDColor.YELLOW)
        
        # Odešleme všechny LED najednou
        if not await self.update_all_leds(ws_id):
            return False
        
        if ws_id:
            led_sections = [str(section.led_section) for section in sections if section.led_section is not None]
            await ws_manager.send(ws_id, {
                "type": "led_update",
                "status": "initialized",
                "message": f"LED systém byl inicializován - LED sekce {', '.join(led_sections)} svítí žlutě"
            })
        
        return True

locker_manager = LockerManager()