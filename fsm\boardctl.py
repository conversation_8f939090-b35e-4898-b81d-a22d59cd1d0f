import argparse
import sys
import os
import serial
import crcmod.predefined
import sliplib
import enum
import subprocess


VERSION             = '3.9'
DEFAULT_BAUDRATE    = 115200
READ_TIMEOUT        = 2         # seconds
ARGUMENT_COUNT      = 5         # max number of arguments for a command (16-bit wide)
TRANSMIT_TRIALS     = 3
PROGRAMMER_FILE     = r"C:\Program Files\STMicroelectronics\STM32Cube\STM32CubeProgrammer\bin\stm32_programmer_cli.exe"
FIRMWARE_FILE       = r"C:\source\3iD\3iD_v3.10.bin"
DEBUG_OUTPUT_ENABLE = False
DEBUG_OUTPUT        = None if DEBUG_OUTPUT_ENABLE else subprocess.DEVNULL


RETURN_CODES = {

    "success":              0,
    "argument_error":       1,
    "invalid_frame":        2,
    "error_notification":   3,
    "invalid_action":       4
}

COLOR_NAMES = {
    
    "NONE":                 0,
    "A":                    1,
    "B":                    2,
    "C":                    3,
    "D":                    4
}

BOOLEAN_VALUES = {

    "0"     : 0,
    "1"     : 1,
    "off"   : 0,
    "on"    : 1,
}


class Command( enum.Enum ):

    undefined                   = 0
    error                       = 1

    firmware_version            = 2
    firmware_restart            = 3

    read_input                  = 4
    clear_input                 = 5
    enable_input                = 6
    read_output                 = 7
    write_output                = 8

    read_light                  = 9
    enable_light                = 10
    read_box_light              = 11
    write_box_light             = 12

    lock_all                    = 13
    unlock_box_door             = 14
    unlock_service_door         = 15
    read_box_door               = 16
    read_service_door           = 17
    enable_service_door         = 18

    read_temperature            = 19

    
    
    read_lock_config            = 20
    write_lock_config           = 21
    
    read_outer_light_config     = 22
    write_outer_light_config    = 23

    read_led_color_config       = 24
    write_led_color_config      = 25
    read_led_segment_config     = 26
    write_led_segment_config    = 27

    read_fan_config             = 28
    write_fan_config            = 29

    read_current_config         = 30
    write_current_config        = 31

    read_heating_config         = 32
    write_heating_config        = 33
    
    read_error_register         = 34
    clear_error_register        = 35

    test_fan                    = 36
    test_light                  = 37
    test_output                 = 38

    read_inner_light_config     = 39
    write_inner_light_config    = 40

    run_bootloader              = 50


# SUCCESS   - when parsing response, return status + results
#   1       SUCCESS
#   0       NO EFFECT

# FIRMWARE FAILURE  - when parsing response, return status, FirmwareException
#  -1       INVALID ARGUMENT
#  -2       INVALID COMMAND
#  -3       HARDWARE FAILURE
#  -4       NOT AVAILABLE
#  -5       UNKOWN STATUS

# COMMUNICATION ERROR -> REPEAT - when parsing response or waiting for response, CommException exception
# -10       TRANSMISSION ERROR (invalid length or CRC)
# -11       RECEPTION ERROR (invalid length or crc)
# -12       TIMEOUT
# -13       UNEXPECTED RESPONSE

# IMPOSSIBLE TO EXECUTE - before transmission
# -20       CANNOT OPEN PORT
# -21       INVALID ACTION PARAMETER
# -22       INVALID VALUE PARAMETER
# -23       INVALID PARAMETER COUNT
# -24       INVALID USAGE FORMAT



#   ARGUMENT PARSING
#   ────────────────────────────────────────────────────────────────────────────────────────────────

def padEnd( input, padValue, padLength ):

    return (input + [padValue] * padLength)[ 0:padLength ]

def computeCrc( data ):

    crcFunction = crcmod.predefined.mkPredefinedCrcFun( 'crc-ccitt-false' )
    return crcFunction( data )

def int16( bytes, signed ):

    return int.from_bytes( bytes, byteorder='little', signed=signed )


def parseInteger( input, min, max, shift = 0 ):
    
    result = int( input )
    
    if( result < min or result > max ):
        raise ArgumentValueException( result )
    
    return result + shift


def parseFloat( input, min = -30.0, max = 6500.0 ):
    
    result = float( input )
    
    if( result < min or result > max ):
        raise ArgumentValueException( result )

    return int( result * 10 ) 


def parseBoolean( input ):

    value = BOOLEAN_VALUES.get( input )
    if( value != None ): return value

def parseColorName( input ):
    
    if input == "none": return 0
    if len( input ) == 1 and input >= "a" and input <= "z": return ord( input ) - ord( "a" ) + 1


def serializeInteger( value, signed = False ):
    return int16( value, signed )

def serializeFloat( value, divisor = 10 ):
    return int16( value, True ) / divisor

def serializeColor( value ):
    return ([ key for key, _value in COLOR_NAMES.items() if _value == int16( value, False )]+["?"])[ 0 ]

def serializeBitfield( bytes, width, count ):
    mask    = 2**width - 1
    value   = int16(bytes, False)
    return [ (value >> x) & mask for x in range( 0, width * count, width ) ]

def serializeVersion( value ):
    integer = int16( value, False )
    major   = integer // 1000
    minor   = integer % 1000
    return f'{major}.{minor}'


BOOL            = lambda x: parseBoolean( x )
COLOR           = lambda x: parseColorName( x )
ACTION          = lambda x: parseInteger( x,  0, 65000 )
LOCK            = lambda x: parseInteger( x,  1,   206 )
RGB             = lambda x: parseInteger( x,  0,   255 )
CHANNEL         = lambda x: parseInteger( x,  1,     8 )
STRIP           = lambda x: parseInteger( x,  1,     4 )
LED             = lambda x: parseInteger( x,  1,   120 )
INPUT           = lambda x: parseInteger( x,  1,     8 )
OUTPUT          = lambda x: parseInteger( x,  1,    15 )
LIGHT           = lambda x: parseInteger( x,  1,     3 )
HEATING         = lambda x: parseInteger( x,  1,     2 )
TIMEOUT_LOCK    = lambda x: parseInteger( x, 15,   300 )
TIMEOUT_OUTER   = lambda x: parseInteger( x,  0,   300 )
TIMEOUT_INNER   = lambda x: parseInteger( x,  5,   300 )
TIME            = lambda x: parseInteger( x,  0,   600 )
FAN             = lambda x: parseInteger( x,  1,     4 )
CURRENT         = lambda x: parseInteger( x,  0,  1000 )
MODE            = lambda x: parseInteger( x,  0,     2 )
TEMP            = lambda x: parseFloat( x, -50,  120 )
TEMP_POINT      = lambda x: parseFloat( x, -30,   30 )
TEMP_DIFF       = lambda x: parseFloat( x,   0,   10 )
TEMP_NEUTRAL    = lambda x: parseFloat( x,   0,   20 )
TEMP_MINIMUM    = lambda x: parseFloat( x, -10,   30 )
TEMP_MAXIMUM    = lambda x: parseFloat( x,   0,   70 )

O_VERSION       = serializeVersion
O_COLOR         = serializeColor
O_TEMP          = serializeFloat
O_CURRENT       = serializeInteger
O_ID            = serializeInteger
O_RGB           = serializeInteger
O_STATE         = serializeInteger
O_TIME          = serializeInteger
O_ERROR_REG_1   = serializeInteger
O_ERROR_REG_2   = lambda x: serializeBitfield( x, 2, 4 )
O_ERROR_REG_3   = lambda x: serializeBitfield( x, 1, 1 )


ACTIONS = {

    
    "unlock":                       (Command.unlock_box_door,               [LOCK, False],                  []),
    "lock_tempered":                (Command.lock_all,                      [],                             []),
    "unlock_tempered":              (Command.unlock_box_door,               [LOCK, True],                   []),
    "unlock_service":               (Command.unlock_service_door,           [0, True],                      []),
    "unlock_service_test":          (Command.unlock_service_door,           [0, False],                     []),
    "write_lock_timeout":           (Command.write_lock_config,             [TIMEOUT_LOCK],                 []),
    "read_lock_timeout":            (Command.read_lock_config,              [],                             [O_TIME]),
    "read_lock_state":              (Command.read_box_door,                 [LOCK],                         [None, O_STATE]),
    "read_service_lock_state":      (Command.read_service_door,             [],                             [None, O_STATE, O_STATE]),
    "enable_service_lock":          (Command.enable_service_door,           [BOOL, BOOL],                   []),

    "read_security_state":          (Command.read_input,                    [2],                            [None, O_STATE, O_STATE, O_TIME]),
    "clear_security_state":         (Command.clear_input,                   [2],                            []),
    "enable_security":              (Command.enable_input,                  [2, True],                      []),
    "disable_security":             (Command.enable_input,                  [2, False],                     []),
    "read_ups_state":               (Command.read_input,                    [7],                            [None, O_STATE, O_STATE, O_TIME]),
    "clear_ups_state":              (Command.clear_input,                   [7],                            []),
    "enable_ups":                   (Command.enable_input,                  [7, True],                      []),
    "disable_ups":                  (Command.enable_input,                  [7, False],                     []),
    
    "read_temperature":             (Command.read_temperature,              [CHANNEL],                      [O_ID, O_TEMP]),
    "write_main_heating_config":    (Command.write_heating_config,          [HEATING, TEMP_POINT, TEMP_DIFF, TEMP_DIFF, TEMP_NEUTRAL],  []),
    "read_main_heating_config":     (Command.read_heating_config,           [HEATING],                      [O_ID, O_TEMP, O_TEMP, O_TEMP, O_TEMP]),
    "write_service_heating_config": (Command.write_heating_config,          [3, TEMP_MINIMUM, TEMP_DIFF],   []),
    "read_service_heating_config":  (Command.read_heating_config,           [3],                            [None, O_TEMP, O_TEMP]),
    "write_service_fan_config":     (Command.write_heating_config,          [4, TEMP_MAXIMUM, TEMP_DIFF],   []),
    "read_service_fan_config":      (Command.read_heating_config,           [4],                            [None, O_TEMP, O_TEMP]),
    "write_rear_fan_config":        (Command.write_fan_config,              [FAN, TIME, TIME],              []),
    "read_rear_fan_config":         (Command.read_fan_config,               [FAN],                          [None, O_TIME, O_TIME]),
    "write_fan_current_config":     (Command.write_current_config,          [FAN, CURRENT, CURRENT],        []),
    "read_fan_current_config":      (Command.read_current_config,           [FAN],                          [None, O_CURRENT, O_CURRENT, O_CURRENT]),
    "test_fan":                     (Command.test_fan,                      [FAN, MODE],                    [O_ID, O_STATE, O_CURRENT]),
    
    "enable_led":                   (Command.enable_light,                  [3, True],                      []),
    "disable_led":                  (Command.enable_light,                  [3, False],                     []),
    "read_led_state":               (Command.read_light,                    [3],                            [None, O_STATE, O_STATE]),
    "clear_all_led":                (Command.write_box_light,               [0, 0, 0, 0, 1],                []),
    "write_led_colors":             (Command.write_box_light,               [LOCK, COLOR],                  [], True),
    "write_segment_config":         (Command.write_led_segment_config,      [LOCK, STRIP, LED, LED],        []),
    "read_segment_config":          (Command.read_led_segment_config,       [LOCK],                         [O_ID, O_ID, O_ID, O_ID]),
    "write_color_config":           (Command.write_led_color_config,        [COLOR, RGB, RGB, RGB],         []),
    "read_color_config":            (Command.read_led_color_config,         [COLOR],                        [O_COLOR, O_RGB, O_RGB, O_RGB]),
    "read_inner_light_state":       (Command.read_light,                    [1],                            [None, O_STATE, O_STATE]),
    "enable_inner_light":           (Command.enable_light,                  [1, True],                      []),
    "disable_inner_light":          (Command.enable_light,                  [1, False],                     []),
    "read_outer_light_state":       (Command.read_light,                    [2],                            [None, O_STATE, O_STATE]),
    "enable_outer_light":           (Command.enable_light,                  [2, True],                      []),
    "disable_outer_light":          (Command.enable_light,                  [2, False],                     []),
    "write_outer_light_config":     (Command.write_outer_light_config,      [TIMEOUT_OUTER],                []),
    "read_outer_light_config":      (Command.read_outer_light_config,       [],                             [O_TIME]),
    "write_inner_light_timeout":    (Command.write_inner_light_config,      [TIMEOUT_INNER],                []),
    "read_inner_light_timeout":     (Command.read_inner_light_config,       [],                             [O_TIME]),
    "test_light":                   (Command.test_light,                    [LIGHT, MODE],                  [O_ID, O_STATE]),
    
    "read_input":                   (Command.read_input,                    [INPUT],                        [None, O_STATE, O_STATE, O_TIME]),
    "clear_input":                  (Command.clear_input,                   [INPUT],                        []),
    "enable_input":                 (Command.enable_input,                  [INPUT, True],                  []),
    "disable_input":                (Command.enable_input,                  [INPUT, False],                 []),
    "read_output":                  (Command.read_output,                   [OUTPUT],                       [None, O_STATE]),
    "write_output":                 (Command.write_output,                  [OUTPUT, BOOL],                 []),
    "test_output":                  (Command.test_output,                   [OUTPUT, MODE],                 [O_ID, O_STATE]),

    "read_error_register":          (Command.read_error_register,           [],                             [O_ERROR_REG_1, O_ERROR_REG_2, O_ERROR_REG_3]),
    "clear_error_register":         (Command.clear_error_register,          [],                             []),
    
    "firmware_version":             (Command.firmware_version,              [],                             [O_VERSION]),
    "hardware_version":             (Command.firmware_version,              [],                             [None, O_VERSION]),
    "firmware_restart":             (Command.firmware_restart,              [],                             []),
    "run_bootloader":               (Command.run_bootloader,                [],                             []),
}



class CustomException( Exception ):
    pass

class FwInvalidArgument( CustomException ):
    def __init__( self, status ): CustomException.__init__( self, f"Firmware Exception: {status}", (-1) )

class FwInvalidCommand( CustomException ):
    def __init__( self, status ): CustomException.__init__( self, f"Firmware Exception: {status}", (-2) )

class FwHardwareFailure( CustomException ):
    def __init__( self, status ): CustomException.__init__( self, f"Firmware Exception: {status}", (-3) )

class FwNotAvailable( CustomException ):
    def __init__( self, status ): CustomException.__init__( self, f"Firmware Exception: {status}", (-4) )

class TransmissionException( CustomException ):
    def __init__( self, text ): CustomException.__init__( self, "Transmission Exception", (-10) )

class ReceptionException( CustomException ):
    def __init__( self ): CustomException.__init__( self, "Reception Exception", (-11 ) )

class TransmissionTimeoutException( CustomException ):
    def __init__( self ): CustomException.__init__( self, "Transmission Timeout Exception", (-12) )

class UnexpectedResponseException( CustomException ):
    def __init__( self, expected, received ): CustomException.__init__( self, f"Command expected {expected}, got {received}", (-13) )

class PortAccessException( CustomException ):
    def __init__( self, device ): CustomException.__init__( self, f"Cannot open port: {device}", (-20) )

class InvalidActionException( CustomException ):
    def __init__( self, action ): CustomException.__init__( self, f"Invalid Action '{ action }'", (-21) )

class ArgumentValueException( CustomException ):
    def __init__( self, value ): CustomException.__init__( self, f"Invalid argument value '{value}'", (-22) )

class ArgumentCountException( CustomException ):
    def __init__( self, expected, received ): CustomException.__init__( self, f'Arguments expected {expected}, got {received}.', (-23) )

class CallFormatException( CustomException ):
    def __init__( self ): CustomException.__init__( self, "Missing --device or --action argument", (-24) )

class UpdateUtilityException( CustomException ):
    def __init__( self ): CustomException.__init__( self, f"Cannot run firmware update utility: {PROGRAMMER_FILE}", (-30) )

class BootloaderException( CustomException ):
    def __init__( self ): CustomException.__init__( self, f"Communication with bootloader has failed", (-31) )

class UpdateFormatException( CustomException ):
    def __init__( self ): CustomException.__init__( self, f"Firmware file must be .bin format (filename must end with \".bin\")", (-32) )

class UpdateFileException( CustomException ):
    def __init__( self ): CustomException.__init__( self, f"Cannot open firmware file: {FIRMWARE_FILE}", (-33) )






def printVersion():

    print( VERSION )

def printActions():
    
    # print('{:<30} {:<10}'.format('action', 'args'))

    for [action, data] in ACTIONS.items():

        # input = data[ 2 ].split( "|" )
        # input = ", ".join( input )
        # input = f"({input})"

        # output = [ item for item in data[ 3 ].split( "|" ) if item != "-" ]
        # output = ", ".join( output )
        # output = f"({output})"

        # print('{:<30} {:<20} -> {:<10}'.format( action, input, output ) )
        print('{:<30}'.format( action ) )




#   ARGUMENT PARSING
#   ────────────────────────────────────────────────────────────────────────────────────────────────

def parseInputArgs( inputs, types, repeated = False ):

    result = []

    # print( f"Parsing args {inputs} as {types}" )

    if repeated:
        types = types * int( len( inputs ) / len( types ) )
    
    user_args = [type for type in types if callable( type )]
    
    if len( user_args ) != len( inputs ):
        raise ArgumentCountException( len( user_args ), len( inputs ) )

    for arg_type in types:

        try:

            if callable( arg_type ):
                input  = inputs.pop( 0 )
                input  = str( input ).lower()
                value  = arg_type( input )
                # print( "Argument parsed as ", value, type( value ) )
            
            else:
                value  = int( arg_type )

            if not type( value ) is int:
                raise Exception
            
            result.append( value )
        
        except CustomException as exception:
            
            raise exception

        except Exception as exception:

            # raise exception
            raise ArgumentValueException( input )
    
    return result


def parseOutput( message, expectedCommand, types ):

    message = message[ -1 ]
    command = message[ 0 ]
    status  = message[ 1 ]

    if command == Command.error.value:

        raise TransmissionException( status )

    elif command != expectedCommand:

        raise UnexpectedResponseException( expectedCommand, command )
    
    elif status == 1 or status == 2:

        values  = [ message [ x : x+2 ] for x in range( 2, 12, 2 ) ]
        result  = [ 0 if status == 2 else status ]

        for (value, type_def) in zip( values, types ):

            if callable( type_def ):
                parsed_value = type_def( value )
                if type( parsed_value ) is list:
                    result.extend( parsed_value )
                else:
                    result.append( parsed_value )
        
        return result
    
    elif status == 3: return [ -4 ]     # not available
    elif status == 4: raise FwHardwareFailure( "Hardware failure" )
    elif status == 5: return [ -10 ]    # invalid length
    elif status == 6: return [ -10 ]    # invalid crc
    elif status == 7: return [ -13 ]    # invalid command
    elif status >= 8 and status <= 12: return [ -1, status - 7 ]    # invalid argument 0..4
    else: return [ -5 ]                 # unkown status

    #  -1       INVALID ARGUMENT
    #  -2       INVALID COMMAND
    #  -3       HARDWARE FAILURE
    #  -4       NOT AVAILABLE
    #  -5       UNKOWN STATUS
    # -10       TRANSMISSION ERROR
    # -13       UNEXPECTED RESPONSE


#   ────────────────────────────────────────────────────────────────────────────────────────────────

def createMessage( type, args ):

    payload     = [type] + padEnd( args, 0, ARGUMENT_COUNT )
    payload     = [ byte for integer in payload for byte in integer.to_bytes( 2, byteorder='little', signed = True ) ]
    payload     = bytes( payload )
    
    crc         = computeCrc( payload )
    crc         = crc.to_bytes( 2, byteorder='little' )

    message     = payload + crc

    return message


def createRequests( type, args, multiRequest ):

    if not multiRequest:
        message = createMessage( type, args )
        return [message]

    messageCount = int( (len( args ) + 1) / 2)

    if messageCount == 0:
        messageArgs = [0, 0, 0, 0, 1]
        message = createMessage( type, messageArgs )
        return [message]

    requests = []

    for messageIdx in range( 0, messageCount ):

        messageArgs = args[ 2*messageIdx : 2*messageIdx + 2 ] + [ 0, messageIdx, messageCount ]
        message = createMessage( type, messageArgs )
        requests.append( message )

    return requests


def transmitRequest( device, request ):

    # print( f'Transmitting: {[int(x) for x in request]}' )

    encoded         = bytes([0x7F]) + sliplib.encode( request )
    timeouts        = 0
    corruptions     = 0
    response        = None

    try:
        port            = serial.Serial( device, DEFAULT_BAUDRATE )
    except:
        raise PortAccessException( device )
    
    port.timeout    = READ_TIMEOUT

    for trial in range( 0, TRANSMIT_TRIALS ):
    
        port.flush()
        port.write( encoded )

        response = port.read_until( expected=sliplib.END )
        
        if not response or len( response ) < 1 or response[-1] != 0xC0:
            timeouts += 1
            # print( "TIMEOUT" )
            continue
        
        # try:
        #     print( '[debug output]:\n', response[:-1].decode( encoding="ascii" ), '\n' )
        # except Exception:
        #     print( "[cannot decode response as ASCII]")
            
        response = port.read_until( expected=sliplib.END )

        if response[-1] != 0xC0:
            timeouts += 1
            # print( "TIMEOUT" )
            continue

        try:
            response = sliplib.decode( response )
            # print( '[response]:', [ int(x) for x in response ] )

        except:
            corruptions += 1
            # print( "SLIP DECODE FAILURE" )
            continue
        
        validCrc    = computeCrc( response[ 0:12 ] )
        msgCrc      = int.from_bytes( response[ 12:14 ], byteorder='little' )

        if( msgCrc != validCrc ):
            corruptions += 1
            # print( f"CRC Expected: {validCrc}, received: {msgCrc}" )
            continue
        
        elif response[0] == Command.error.value:
            corruptions += 1
            # print( f"Error message received: {response[1]}")
            continue
        
        else:
            # print( f"Communication success! ({trial} errors)" )
            break
        
    port.close()

    if (timeouts + corruptions >= TRANSMIT_TRIALS):
        
        if corruptions == 0:
            raise TransmissionTimeoutException() # f"{TRANSMIT_TRIALS} trial(s), {READ_TIMEOUT} second(s) wait period" )

        else:
            raise TransmissionException()

    
    return response


def transmitRequests( device, requests ):

    return [ transmitRequest( device, request ) for request in requests ]



def transaction( port, command, userArgs, inputArgs, outputArgs, multiRequest ):

    parsedArgs  = parseInputArgs( userArgs, inputArgs, multiRequest )
    requests    = createRequests( command, parsedArgs, multiRequest )
    responses   = transmitRequests( port, requests )
    output      = parseOutput( responses, command, outputArgs )

    return output


def executeAction( userInput, port ):

    actionName  = userInput[ 0 ]
    actionName  = str( actionName ).lower()
    actionArgs  = userInput[ 1: ]

    # if actionName == "stlink":
    #     print( "ACTION= STLINK", actionArgs )
    #     upload_firmware_file( port, actionArgs[ 0 ] )
    #     return

    action      = ACTIONS.get( actionName )

    if type( action ) != tuple:
        raise InvalidActionException( actionName )

    command         = action[ 0 ].value
    inputArgs       = action[ 1 ]
    outputArgs      = action[ 2 ]
    multiRequest    = len( action ) > 3 and action[ 3 ]
    updateCommand   = command == Command.run_bootloader.value

    if updateCommand:
        test_required_files()

    try:
        response        = transaction( port, command, actionArgs, inputArgs, outputArgs, multiRequest )
        # success         = response[ 0 ] == 1
    
    except Exception as ex:
        if not updateCommand: raise ex

    if updateCommand: # and success:
        return upload_firmware_file( port )

    else:
        return response


def upload_firmware_file( port, file = FIRMWARE_FILE ):

    command = f"{PROGRAMMER_FILE} -c port={port} -w {file} 0x8000000 -v"
    # print( command )
    success = subprocess.call( command, stdout=DEBUG_OUTPUT ) == 0

    if success:

        # command = f"{PROGRAMMER_FILE} -c port={port} -ob nSWBOOT0=1 nBOOT0=1"       # bootloader: 73EFF8AA, app: 7FEFF8AA
        command = f"{PROGRAMMER_FILE} -c port={port} -w32 0x40022040 0x7FEFF8AA"
        # print( command )
        success = subprocess.call( command, stdout=DEBUG_OUTPUT ) == 0

        return [1]
    
    else:
        raise BootloaderException()
    

def test_required_files():

    if not FIRMWARE_FILE.endswith( ".bin" ):
        raise UpdateFormatException()

    try:
        file = open( FIRMWARE_FILE, mode="rb" )
        file.read()
        file.close()
    
    except:
        raise UpdateFileException()

    try:
        subprocess.call( f"{PROGRAMMER_FILE}", stdout=DEBUG_OUTPUT )
    
    except:
        raise UpdateUtilityException()



#   MAIN
#   ────────────────────────────────────────────────────────────────────────────────────────────────

def main():
    
    parser = argparse.ArgumentParser()
    parser.add_argument( '-a', '--action',    action='store',         help='Do specified action',         nargs="*" )
    parser.add_argument( '-d', '--device',    action='store',         help='which serial device to use' )
    parser.add_argument( '-v', '--version',   action='store_true',    help='get script version' )
    parser.add_argument( '-l', '--list',      action='store_true',    help='print all possible actions' )
    args = parser.parse_args()

    
    if type( args.device ) == str and args.device.startswith( "COM" ):
        
        args.device = "\\\\.\\" + args.device  # Needed to address COM ports >= 10
    


    if args.version:
        
        return [1, VERSION]

    elif args.list:
        
        printActions()
        sys.exit( 1 )
    
    elif args.device is None:
        
        raise CallFormatException()
    
    elif not os.path.exists( args.device ):
        
        raise PortAccessException( args.device )

    elif args.action is None or len( args.action ) < 1:
        
        raise CallFormatException()
    
    else:
        
        return executeAction( args.action, args.device )



if __name__ == '__main__':

    try:

        result   = main()
        exitCode = result[ 0 ]
        result   = " ".join( map( str, result ) )
        message  = "Script finished successfuly"

    except CustomException as exception:

        message  = exception.args[ 0 ]
        exitCode = exception.args[ 1 ]
        result   = exitCode

    # except Exception as exception:

    #     message  = exception.args[ 0 ]
    #     exitCode = -30
    #     result   = exitCode

    print( message )
    print( result )
    sys.exit( exitCode )
