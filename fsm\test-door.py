import subprocess
import sys
import logging
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

hardware_port = os.getenv("HARDWARE_PORT", "COM4")
electronic_version = os.getenv("ELECTRONIC_VERSION", "1.0")
python_path = os.getenv("PYTHON_PATH", "python")

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def send_command(locker_id: int, command: str = "unlock_tempered"):
    """
    Pošle příkaz na hardware skříňky přes boardctl.py

    Args:
        locker_id: ID sk<PERSON> (1-206)
        command: př<PERSON>az (unlock_tempered, lock_tempered)
    """
    try:
        # Get hardware port from environment variable, fallback to COM4
        cmd = [python_path, "boardctl.py", "-d", hardware_port, "-a", command, locker_id]
        
        logger.info(f"Executing command: {' '.join(map(str, cmd))}")
        
        # Spustíme příkaz
        proc = subprocess.Popen(
            [str(x) for x in cmd],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        stdout, stderr = proc.communicate()
        
        stdout_text = stdout.decode().strip()
        stderr_text = stderr.decode().strip()
        
        logger.info(f"Command output - stdout:\n{stdout_text}")
        if stderr_text:
            logger.info(f"Command output - stderr:\n{stderr_text}")
            
        return stdout_text
            
    except Exception as e:
        logger.error(f"Error executing command: {str(e)}")
        return None

if __name__ == "__main__":
    # Výchozí hodnoty
    locker_id = 42  # Výchozí ID skříňky
    command = "unlock_tempered"  # Výchozí příkaz
    
    # Zpracování argumentů
    if len(sys.argv) > 1:
        try:
            locker_id = int(sys.argv[1])
            if locker_id < 1 or locker_id > 206:
                logger.error(f"Invalid locker ID {locker_id} - must be between 1 and 206")
                sys.exit(1)
        except ValueError:
            logger.error(f"Invalid locker ID format - must be a number")
            sys.exit(1)
    
    if len(sys.argv) > 2:
        command = sys.argv[2]
        if command not in ["unlock_tempered", "lock_tempered"]:
            logger.error(f"Invalid command {command} - must be unlock_tempered or lock_tempered")
            sys.exit(1)
    
    # Spustíme příkaz
    logger.info(f"Testing door {locker_id} with command {command}")
    result = send_command(locker_id, command)
    
    if result:
        logger.info("Command executed successfully")
    else:
        logger.error("Command failed")
        sys.exit(1) 