from fastapi import <PERSON><PERSON><PERSON>, WebSocket, HTTPException, Body
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from typing import List, Dict, Optional
from managers.locker_manager import locker_manager
from managers.ws_manager import ws_manager
import uuid
from pydantic import BaseModel
import asyncio

app = FastAPI()

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

class SectionConfig(BaseModel):
    section_id: int
    is_tempered: bool
    led_section: Optional[int] = None

class PinValidationRequest(BaseModel):
    pin: str

class PinValidationResponse(BaseModel):
    session_id: str
    sections: List[SectionConfig]

# Extended model for session
class SessionData(BaseModel):
    sections: List[SectionConfig]
    auto_mode: bool = False
    pin: str
    status: str = "pending"  # pending, ready, in_progress, completed, failed
    current_section_index: Optional[int] = None

# Dictionary for storing session data
active_sessions: Dict[str, SessionData] = {}

# Fallback PIN for testing
TEST_PIN = "123456"
# Testovací konfigurace sekcí
TEST_SECTIONS = [
    SectionConfig(section_id=1, is_tempered=True, led_section=1),   # Temperovaná s LED
    SectionConfig(section_id=2, is_tempered=True, led_section=2),   # Temperovaná s LED
    SectionConfig(section_id=3, is_tempered=True, led_section=3),   # Temperovaná s LED
    SectionConfig(section_id=4, is_tempered=False, led_section=None),   # Netemperovaná bez LED
]

@app.get("/")
async def root():
    return FileResponse("static/index.html")

@app.post("/api/validate-pin", response_model=PinValidationResponse)
async def validate_pin(request: PinValidationRequest):
    """
    Validate PIN and return session_id and list of sections with their configurations.
    For testing uses fallback PIN 123456.
    """
    if request.pin != TEST_PIN:
        raise HTTPException(status_code=401, detail="Invalid PIN")
    
    # Create new session
    session_id = str(uuid.uuid4())
    
    # Save session data
    active_sessions[session_id] = SessionData(
        sections=TEST_SECTIONS,
        pin=request.pin,
        status="pending"
    )
    
    return PinValidationResponse(
        session_id=session_id,
        sections=TEST_SECTIONS
    )

@app.get("/api/sequence-status/{session_id}")
async def get_sequence_status(session_id: str):
    """
    Return current sequence status for polling
    """
    if session_id not in active_sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    session = active_sessions[session_id]
    return {
        "status": session.status,
        "current_section_index": session.current_section_index,
        "sections": session.sections
    }

class LockerSequenceRequest(BaseModel):
    locker_ids: List[str]
    auto_mode: bool = False

# Dictionary for storing locker sequences
locker_sequences: Dict[str, dict] = {}

@app.post("/start-sequence")
async def start_sequence(request: LockerSequenceRequest):
    """
    Start new sequence of opening lockers.
    Return sequence_id, which is used for WebSocket connection.
    """
    if not request.locker_ids:
        raise HTTPException(status_code=400, detail="No lockers specified")
    
    # Generate unique sequence ID
    sequence_id = str(uuid.uuid4())
    locker_sequences[sequence_id] = {
        "locker_ids": request.locker_ids,
        "auto_mode": request.auto_mode
    }
    
    return {
        "sequence_id": sequence_id,
        "locker_count": len(request.locker_ids),
        "lockers": request.locker_ids,
        "auto_mode": request.auto_mode
    }

@app.post("/unlock/{locker_id}")
async def unlock(locker_id: str):
    """
    Original endpoint for one locker - kept for backward compatibility
    """
    try:
        await locker_manager.start_unlock_sequence(locker_id)
        return {"status": "started"}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))

async def handle_locker_sequence(
    sequence_id: str,
    section_config: SectionConfig,
    websocket: WebSocket,
    total_lockers: int,
    current_idx: int
) -> bool:
    """
    Handle sequence for a single locker with its configuration
    """
    try:
        # Oznámení začátku odemykání
        await ws_manager.send(sequence_id, {
            "type": "section_update",
            "section_id": section_config.section_id,
            "status": "processing",
            "message": f"Schránka č. {section_config.section_id} se odemyká..."
        })

        # Start unlock sequence - nyní neblokuje díky background taskům
        result = await locker_manager.start_unlock_sequence(
            str(section_config.section_id),
            is_tempered=section_config.is_tempered,
            session_id=sequence_id,
            led_section=section_config.led_section
        )
        
        if not result["success"]:
            await ws_manager.send(sequence_id, {
                "type": "section_update",
                "section_id": section_config.section_id,
                "status": "error",
                "message": f"Chyba při odemykání schránky č. {section_config.section_id}: {result.get('error', 'Neznámá chyba')}"
            })
            return False
        
        # locker_manager nyní posílá zprávy automaticky přes background task
        # Čekáme na dokončení celé sekvence pro všechny typy schránek
        completion_success = await locker_manager.wait_for_sequence_completion(
            str(section_config.section_id),
            timeout=300  # 5 minut timeout
        )
        
        if not completion_success:
            await ws_manager.send(sequence_id, {
                "type": "section_update",
                "section_id": section_config.section_id,
                "status": "timeout",
                "message": f"Schránka č. {section_config.section_id} - timeout při čekání na dokončení sekvence"
            })
            return False
        
        return True
        
    except Exception as e:
        await ws_manager.send(sequence_id, {
            "type": "section_update",
            "section_id": section_config.section_id,
            "status": "error",
            "message": f"Chyba při odemykání schránky č. {section_config.section_id}: {str(e)}"
        })
        return False

@app.websocket("/ws/{session_id}")
async def websocket_endpoint(websocket: WebSocket, session_id: str):
    """
    WebSocket endpoint for sequence of sections.
    Implements new protocol with ready_for_sequence.
    """
    if "sec-websocket-protocol" in websocket.headers:
        await websocket.accept(subprotocol=websocket.headers["sec-websocket-protocol"])
    else:
        await websocket.accept()
    
    # Check existence of session
    if session_id not in active_sessions:
        await websocket.close(code=4000, reason="Invalid session ID")
        return
    
    session = active_sessions[session_id]
    
    # Clean up possible old connection
    if session_id in ws_manager.connections:
        old_ws = ws_manager.connections[session_id]
        try:
            await old_ws.close(code=1000)
        except:
            pass
        ws_manager.disconnect(session_id)
    
    await ws_manager.connect(session_id, websocket)
    
    try:
        # Send initial message with pending state
        await ws_manager.send(session_id, {
            "type": "init",
            "sections": [
                {
                    "id": section.section_id,
                    "status": "pending",
                    "is_tempered": section.is_tempered,
                    "led_section": section.led_section
                }
                for section in session.sections
            ]
        })
        
        # Wait for ready_for_sequence from client
        try:
            message = await websocket.receive_json()
            if message.get("type") != "ready_for_sequence":
                raise ValueError("Expected ready_for_sequence message")
        except Exception as e:
            await websocket.close(code=4002, reason="Invalid ready message")
            return
        
        # Confirm sequence start
        session.status = "in_progress"
        await ws_manager.send(session_id, {
            "type": "sequence_starting",
            "sections": [
                {
                    "id": section.section_id,
                    "status": "in_queue",
                    "is_tempered": section.is_tempered,
                    "led_section": section.led_section
                }
                for section in session.sections
            ]
        })
        
        # Inicializuj LED systém - zapni a nastav všechny na žlutou
        await locker_manager.initialize_sequence_leds(session.sections, session_id)
        
        # Pause for UI animation and WebSocket flush
        await asyncio.sleep(0.8)
        
        success_count = 0
        # Process each section in sequence
        for idx, section in enumerate(session.sections):
            try:
                session.current_section_index = idx
                
                # Update section status
                await ws_manager.send(session_id, {
                    "type": "section_update",
                    "section_id": section.section_id,
                    "status": "processing",
                    "message": f"Starting section process {idx + 1} of {len(session.sections)}"
                })
                
                success = await handle_locker_sequence(
                    sequence_id=session_id,
                    section_config=section,
                    websocket=websocket,
                    total_lockers=len(session.sections),
                    current_idx=idx + 1
                )
                
                if success:
                    success_count += 1
                    
            except Exception as e:
                await ws_manager.send(session_id, {
                    "type": "section_update",
                    "section_id": section.section_id,
                    "status": "error",
                    "message": f"Error: {str(e)}"
                })
                break
        
        # Update session status
        session.status = "completed" if success_count == len(session.sections) else "failed"
        
        # Vypneme LED systém na konci sekvence
        await locker_manager.disable_leds(session_id)
        
        # Sequence completed
        await ws_manager.send(session_id, {
            "type": "sequence_complete",
            "success_count": success_count,
            "total_count": len(session.sections)
        })
        
    except Exception as e:
        session.status = "failed"
        await ws_manager.send(session_id, {
            "type": "error",
            "message": f"Error: {str(e)}"
        })
    finally:
        # Ujistíme se, že LED systém je vypnutý i při chybě
        try:
            await locker_manager.disable_leds(session_id)
        except:
            pass  # Ignorujeme chyby při vypínání LED
        
        ws_manager.disconnect(session_id)
        # Keep session active for possible polling