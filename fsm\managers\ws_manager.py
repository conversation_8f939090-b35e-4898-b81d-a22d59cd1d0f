from fastapi import WebSocket
from typing import Dict, <PERSON>, Any
import json
import asyncio

class WebSocketManager:
    def __init__(self):
        self.connections: Dict[str, WebSocket] = {}

    async def connect(self, sequence_id: str, websocket: WebSocket):
        self.connections[sequence_id] = websocket

    def disconnect(self, sequence_id: str):
        self.connections.pop(sequence_id, None)

    async def send(self, sequence_id: str, message: Union[str, dict]):
        if sequence_id in self.connections:
            try:
                if isinstance(message, dict):
                    await self.connections[sequence_id].send_json(message)
                else:
                    await self.connections[sequence_id].send_text(message)
                
                # Yield control to ensure message is sent immediately
                await asyncio.sleep(0)
            except Exception as e:
                # Pokud se nepodař<PERSON> odeslat zprávu, odpojíme WebSocket
                self.disconnect(sequence_id)

ws_manager = WebSocketManager()