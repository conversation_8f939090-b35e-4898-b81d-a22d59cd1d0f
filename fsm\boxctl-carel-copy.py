import argparse
import operator
import os
import sys
import time
from typing import List, Tuple, Dict

import serial

VERSION = '2.6.0-CAREL'
SPEC_VERSION = '2.1.1-DG300'
OPEN_LOCK_RESPONSE_DELAY = 1  # 1 second


def remove_prefix(text: str, prefix: str) -> str:
    if text.startswith(prefix):  # only modify the text if it starts with the prefix
        text = text.replace(prefix, "", 1)  # remove one instance of prefix
    return text


class Box:
    READ_TIMEOUT = 1  # sec before timeout
    RETURN_CODES = {
        "success": 0,
        "argument_error": 1,
        "invalid_frame": 2,
        "error_notification": 3,
        "invalid_action": 4
    }

    BOX_LED_SEGMENTS_LEN = 160
    BOX_LED_BYTES_LEN: int = BOX_LED_SEGMENTS_LEN // 4
    BOX_LED_COLOR_ORDER: str = ['RGB', 'RBG', 'GRB', 'GBR', 'BRG', 'BGR']

    ACTIONS = {
        "heart_beat": {"id": '01', "len": '00', "rlen": '00', "timeout": READ_TIMEOUT},
        "get_fw_version": {"id": '02', "len": '00', "rlen": '03', "timeout": READ_TIMEOUT},
        "get_hw_version": {"id": '03', "len": '00', "rlen": '02', "timeout": READ_TIMEOUT},
        "get_door_state": {"id": '11', "len": '01', "rlen": '01', "timeout": 35},
        # "open_lock_timeout": {"id": '09', "len": '03', "rlen": '01', "timeout": 255},
        "lock": {"id": '0f', "len": '01', "rlen": '01', "timeout": 35},
        "unlock": {"id": '10', "len": '01', "rlen": '01', "timeout": 35},
        # "unlock_erbi_lock":        {"id": '11', "len": '01', "rlen": '01', "timeout": READ_TIMEOUT},  # N/A
        # "lock_erbi_lock":          {"id": '12', "len": '01', "rlen": '01', "timeout": READ_TIMEOUT},  # N/A
        "update_led_array": {"id": '24', "len": "{0:0{1}X}".format(BOX_LED_BYTES_LEN + 1, 2), "rlen": '01',
                             "timeout": READ_TIMEOUT},
        "clear_led_array": {"id": '25', "len": '01', "rlen": '01', "timeout": READ_TIMEOUT},
        "set_led_color": {"id": '26', "len": '05', "rlen": '01', "timeout": READ_TIMEOUT},
        "set_led_color_order": {"id": '27', "len": '03', "rlen": '01', "timeout": READ_TIMEOUT},
        "control_led_power": {"id": '28', "len": '02', "rlen": '01', "timeout": READ_TIMEOUT},
        "set_temp_setpoint": {"id": '40', "len": '02', "rlen": '02', "timeout": READ_TIMEOUT},
        "get_temperature": {"id": "46", "len": "00", "rlen": '02', "timeout": READ_TIMEOUT},
        "get_temp_setpoint": {"id": "4a", "len": "00", "rlen": '02', "timeout": READ_TIMEOUT},
        "carel_search_sensors_p3": {"id": "51", "len": "00", "rlen": '02', "timeout": READ_TIMEOUT},
        "carel_search_sensors_p4": {"id": "52", "len": "00", "rlen": '02', "timeout": READ_TIMEOUT},
        "carel_set_configuration": {"id": "a0", "len": "09", "rlen": '01', "timeout": 10},
        "carel_get_configuration": {"id": "a1", "len": "01", "rlen": '08', "timeout": 10},
        "carel_save_configuration": {"id": "00", "len": "00", "rlen": '01', "timeout": 10},
        "carel_get_temperature": {"id": "", "len": "00", "rlen": "02", "timeout": READ_TIMEOUT},  # warn: mutiple ids

        "get_open_lock": {"id": "fc", "len": "00", "rlen": '01', "timeout": READ_TIMEOUT},
        "get_error_register": {"id": "fd", "len": "00", "rlen": '04', "timeout": READ_TIMEOUT},
        "error_notification": {"id": "fe", "len": "00", "rlen": '01', "timeout": READ_TIMEOUT},

        "open_service_door_lock": {"id": "30", "len": "02", "rlen": '01', "timeout": READ_TIMEOUT},
        "read_security_state": {"id": "31", "len": "02", "rlen": '01', "timeout": READ_TIMEOUT},
        "clear_security_state": {"id": "32", "len": "02", "rlen": '01', "timeout": READ_TIMEOUT},
        "read_ups_state": {"id": "31", "len": "02", "rlen": '01', "timeout": READ_TIMEOUT},
        "clear_ups_state": {"id": "32", "len": "02", "rlen": '01', "timeout": READ_TIMEOUT},
    }

    CAREL_GET_TEMPERATURE_IDS = ["80", "81", "82", "83", "84", "85", "86", "87",  # MODULe INDEX 3
                                 "88", "90", "91", "92", "93", "94", "95", "96"]  # MODULe INDEX 4

    SOF = "02"
    EOF = "03"

    COLORS = ['A', 'B', 'C', 'NONE']

    # Mapping [box: [LED segmets]]
    BOX_MAPPING: Dict[int, List[int]] = {
        1: [0, 1, 2, 3],
        2: [5, 6, 7, 8],
        3: [10, 11, 12, 13],
        4: [15, 16, 17, 18],
        5: [20, 21, 22, 23],
        6: [25, 26, 27, 28],
        7: [30, 31, 32, 33, 34, 35, 36],
        8: [40, 41, 42, 43],
        9: [45, 46, 47, 48],
        10: [50, 51, 52, 53],
        11: [55, 56, 57, 58],
        12: [60, 61, 62, 63],
        13: [65, 66, 67, 68],
        14: [70, 71, 72, 73, 74, 75, 76],
        21: [80, 81, 82, 83],
        22: [85, 86, 87, 88],
        23: [90, 91, 92, 93],
        24: [95, 96, 97, 98],
        25: [100, 101, 102, 103],
        26: [105, 106, 107, 108],
        27: [110, 111, 112, 113, 114, 115, 116],
        28: [120, 121, 122, 123],
        29: [125, 126, 127, 128],
        30: [130, 131, 132, 133],
        31: [135, 136, 137, 138],
        32: [140, 141, 142, 143],
        33: [145, 146, 147, 148],
        34: [150, 151, 152, 153, 154, 155, 156],
    }

    # Mapping color to byte
    # A = 0b01
    # B = 0b10
    # C = 0b11
    # NONE = 0b00
    COLOR_MAPPING: Dict[str, int] = {
        "A": 0b01,
        "B": 0b10,
        "C": 0b11,
        "NONE": 0b00
    }

    MONITORING_CONTACT_ACTIONS = ['read_security_state', 'clear_security_state', 'read_ups_state', 'clear_ups_state']

    def __init__(self, device: str, baud: int):
        self.ser = serial.Serial(device, baud)
        self.ser.timeout = self.READ_TIMEOUT

    def __exit(self, code):
        """
        Graceful exit from script

        :param code: return code
        :return: nothing
        """
        self.ser.close()
        sys.exit(code)

    @staticmethod
    def rs232_checksum(the_bytes: bytes) -> bytes:
        return b'%02X' % (sum(the_bytes) & 0xFF)

    def get_checksum(self, data: str) -> str:
        return self.rs232_checksum(bytes.fromhex(data)).decode().casefold()

    def get_action(self, action: str, val_args: List[str]) -> bytes:
        return self.__assemble_frame(action, val_args)

    def do_action(self, action: str, val_args: List[str]):
        """
        Execute action on device using serial port

        Sends request into device, prints response
        and exits using corresponding return code.

        :param action: action from the ACTIONS dict
        :param val_args: arguments for given action
        """

        if (action == "read_security_state" or action == "read_ups_state"):
            self._monitoring_contact_information(action)

        req = self.__assemble_frame(action, val_args)
        res = self.__send_to_device(action, req)

        if self.__is_frame_valid(res):
            cid: str = self.__split_to_bytes_list(res).pop(1)
            # if cid == self.ACTIONS['unlock']['id']:
            #     time.sleep(OPEN_LOCK_RESPONSE_DELAY)
            if cid.casefold() == self.ACTIONS['carel_get_configuration']['id'].casefold():
                print(self.__decode_carel_configuration(self.__extract_data(res, ":")))
            elif self.__is_temperature_response(res):
                print(self.__decode_value(self.__extract_data(res), is_temp=True))
            else:
                print(self.__decode_value(self.__extract_data(res)))
            self.__exit(self.RETURN_CODES['success'])
        else:
            print(f"ERR invalid frame detected! {res.hex()}")
            self.__exit(self.RETURN_CODES["invalid_frame"])

    def __decode_value(self, val: str, is_temp: bool = False) -> float:
        """Correctly decode value from frame

        Args:
            val (str): extracted value from frame
            is_temp (bool, optional): Treat value as temperature. Defaults to False.

        Returns:
            float: decoded value
        """
        if is_temp:
            return int.from_bytes(bytes.fromhex(val), signed=is_temp, byteorder='little') / 10
        return int.from_bytes(bytes.fromhex(val), signed=is_temp, byteorder='little')

    # FIXME: works as long as actions with temps includes temp in name
    def __is_temperature_response(self, response: bytes) -> bool:
        """Check if response contains temperature information

        Args:
            response (bytes): received valid frame

        Returns:
            bool: temp is in response
        """
        bytelist: List[str] = self.__split_to_bytes_list(response)
        bytelist.pop(0)  # sof
        cid: str = bytelist.pop(0)

        if cid in self.CAREL_GET_TEMPERATURE_IDS:
            return True

        for key in self.ACTIONS:
            if self.ACTIONS[key]["id"].casefold() == cid.casefold():
                if "temp" in key:
                    return True
                else:
                    return False
        return False

    def monitoring_contact_information(self, action: str, val_args: List[str]):
        """Handle monitoring contact info gathering

        Once done, print gathered information and exit

        Args:
            :param action: action which should be performed
            :param val_args: action arguments
        """
        # output format: "[was active] [is active] [time sec]"
        req_was_active: bytes = self.__assemble_frame(action, [val_args[0], '01'])
        req_is_active: bytes = self.__assemble_frame(action, [val_args[0], '02'])
        req_time: bytes = self.__assemble_frame(action, [val_args[0], '03'])

        if action == "read_security_state":
            req_was_active = self.__assemble_frame(action, [val_args[0], '04'])
            req_is_active = self.__assemble_frame(action, [val_args[0], '05'])
            req_time = self.__assemble_frame(action, [val_args[0], '06'])
        elif action == "clear_ups_state":
            req = self.__assemble_frame(action, [val_args[0], '00'])
            res = self.__send_to_device(action, req)
            if self.__is_frame_valid(res):
                print(self.__extract_data(res))
                self.__exit(self.RETURN_CODES['success'])
            pass
        elif action == "clear_security_state":
            req = self.__assemble_frame(action, [val_args[0], '01'])
            res = self.__send_to_device(action, req)
            if self.__is_frame_valid(res):
                print(self.__extract_data(res))
                self.__exit(self.RETURN_CODES['success'])

        res_was_active: bytes = self.__send_to_device(action, req_was_active)
        res_is_active: bytes = self.__send_to_device(action, req_is_active)
        res_time: bytes = self.__send_to_device(action, req_time)

        if self.__is_frame_valid(res_was_active) and self.__is_frame_valid(res_is_active) and self.__is_frame_valid(
                res_time):
            # res_time is LSB first
            decoded_time = self.__extract_data(res_time)
            time_sec = int(self.__decode_value(decoded_time)) * 15
            active = self.__extract_data(res_is_active)
            was_active = self.__extract_data(res_was_active)

            print(f"{self.__decode_value(was_active)} {self.__decode_value(active)} {time_sec}")
            self.__exit(self.RETURN_CODES['success'])
        else:
            print(f"ERR invalid frame detected! {res_was_active.hex()} {res_is_active.hex()} {res_time.hex()}")
            self.__exit(self.RETURN_CODES["invalid_frame"])

    def __split_to_bytes_list(self, frame: bytes) -> List[str]:
        sf: str = frame.hex()
        s: str = ''
        for sc in map(operator.add, sf[::2], sf[1::2]):
            s += sc + ":"
        s = s[:-1]

        return s.split(':')

    def __split_str_to_bytes_list(self, hex_str: str) -> List[str]:
        s: str = ''
        for sc in map(operator.add, hex_str[::2], hex_str[1::2]):
            s += sc + ":"
        s = s[:-1]

        return s.split(':')

    def __extract_data(self, frame: bytes, join_str='') -> str:
        """
        Extract data bytes from response

        Returned data are joined using ''.

        :param frame: response frame
        :return: data as str
        """
        sf: str = frame.hex()
        s: str = ''
        for sc in map(operator.add, sf[::2], sf[1::2]):
            s += sc + ":"
        s = s[:-1]

        hex_list: List[str] = s.split(':')
        hex_list.pop()  # eof
        hex_list.pop()  # checksum
        hex_list.pop(0)  # sof
        cid: str = hex_list.pop(0)  # cid
        hex_list.pop(0)  # datalen

        if cid.casefold() == self.ACTIONS["error_notification"]["id"].casefold():
            print(':'.join(hex_list))
            self.__exit(self.RETURN_CODES["error_notification"])

        return join_str.join(hex_list)

    def __assemble_frame(self, action: str, val_args: List[str]) -> bytes:
        """
        Create request frame

        Creates req frame from desired action and arguments.
        If action args aren't satisfied exception is thrown.

        :param action: one of actions defined in self.ACTIONS
        :param val_args: arguments corresponding to action
        :return: frame of bytes ready to send
        """

        cid = ""
        if "carel_get_temperature_" in action:
            cid = remove_prefix(action, "carel_get_temperature_")
            if int(cid) - 1 > len(self.CAREL_GET_TEMPERATURE_IDS):
                print(
                    f'action name error please supply after underscore valid ID: 1..{len(self.CAREL_GET_TEMPERATURE_IDS)}')
                print('current mapping id to command:\n\tID\tCMD')
                for id, msgid in enumerate(self.CAREL_GET_TEMPERATURE_IDS):
                    print(f'\t{id + 1}\t0x{msgid}')
                self.__exit(self.RETURN_CODES['argument_error'])
            action = "carel_get_temperature"

        data_len = int.from_bytes(bytes.fromhex(self.ACTIONS[action]["len"]), 'big')

        arg_err: bool = False
        if (val_args is None and data_len != 0) or (val_args is not None and len(val_args) != data_len):
            arg_err = True

        if arg_err and not (action != 'carel_get_configuration' or action != 'carel_set_configuration'):
            print('action argument error')
            self.__exit(self.RETURN_CODES['argument_error'])

        data = ""
        if action == "carel_get_temperature":
            data = self.CAREL_GET_TEMPERATURE_IDS[int(cid) - 1] + self.ACTIONS[action]["len"]
        else:
            data = self.ACTIONS[action]["id"] + self.ACTIONS[action]["len"]
        if val_args is not None:
            for d in val_args:
                data += d
        if "carel_set_configuration" == action:
            data = self.ACTIONS[action]["id"] + self.ACTIONS[action]["len"] + val_args[0]
            for val in val_args[1:]:
                data += int(float(val) * 10).to_bytes(2, "little", signed=True).hex()

        frame: str = self.SOF + data + self.get_checksum(data) + self.EOF
        return bytes.fromhex(frame)

    def __is_frame_valid(self, frame: bytes) -> bool:
        """
        Check if response frame is valid

        :param frame: received response (bytes)
        :return: true if valid
        """
        if frame == b'' or frame is None:
            return False
        sf: str = frame.hex()
        s: str = ''
        for sc in map(operator.add, sf[::2], sf[1::2]):
            s += sc + ":"
        s = s[:-1]

        hex_list: List[str] = s.split(':')
        if hex_list[0] != self.SOF or hex_list[len(hex_list) - 1] != self.EOF:
            return False
        cid: str = hex_list[1]

        cmd: dict = {}
        for key in self.ACTIONS:
            if self.ACTIONS[key]["id"].casefold() == cid.casefold():
                cmd = self.ACTIONS[key]
        if cid in self.CAREL_GET_TEMPERATURE_IDS:
            cmd = self.ACTIONS['carel_get_temperature']
        if not cmd:
            return False
        if len(hex_list) != int.from_bytes(bytes.fromhex(cmd["rlen"]), 'big') + 5:
            return False

        hex_list.pop()  # eof
        f_sum: str = hex_list.pop()  # checksum
        hex_list.pop(0)  # sof

        data: str = ''.join(hex_list)
        if f_sum != self.get_checksum(data):
            return False

        return True

    def __send_to_device(self, action: str, request: bytes) -> bytes:
        """
        Send request into device

        :param action: used action in request
        :param request: whole assembled frame
        :return: response from device
        """

        if "carel_get_temperature_" in action:
            cid = remove_prefix(action, "carel_get_temperature_")
            if int(cid) - 1 > len(self.CAREL_GET_TEMPERATURE_IDS):
                print(
                    f'action name error please supply after underscore valid ID: 1..{len(self.CAREL_GET_TEMPERATURE_IDS)}')
                print('current mapping id to command:\n\tID\tCMD')
                for id, msgid in enumerate(self.CAREL_GET_TEMPERATURE_IDS):
                    print(f'\t{id + 1}\t0x{msgid}')
                self.__exit(self.RETURN_CODES['argument_error'])
            action = "carel_get_temperature"

        # use per action defined timeout
        self.ser.timeout = self.ACTIONS[action]["timeout"]
        resp_len = int.from_bytes(bytes.fromhex(
            self.ACTIONS[action]["rlen"]), 'big') + 5

        self.ser.flush()
        self.ser.write(request)
        resp = self.ser.read(size=resp_len)

        return resp

    def get_status(self):
        import json
        frame = self.__assemble_frame(self.ACTIONS['heart_beat'])
        res = self.__send_to_device(self.ACTIONS['heart_beat'], frame)
        if not self.__is_frame_valid(res):
            # todo: handle weird response
            pass
        data = self.__extract_data(res, join_str=':')
        data_bytes = data.split(':')
        if data_bytes[0].casefold() == '01':
            print(json.dumps({"status": "ok"}))
            self.__exit(self.RETURN_CODES['success'])
        elif data_bytes[0].casefold() == 'fc':
            data_bytes.pop(0)
            # todo: extract message data
        print(json.dumps({"status": "error unknown message id"}))
        self.__exit(self.RETURN_CODES['error_notification'])

    def get_fw_version(self):
        frame = self.__assemble_frame('get_fw_version', None)
        res = self.__send_to_device('get_fw_version', frame)
        if not self.__is_frame_valid(res):
            # todo: handle weird response
            pass
        data = self.__extract_data(res, join_str=':')
        data_bytes = data.split(':')
        major = int.from_bytes(bytes.fromhex(data_bytes[0]), signed=False, byteorder='little')
        minor = int.from_bytes(bytes.fromhex(data_bytes[1]), signed=False, byteorder='little')
        patch = int.from_bytes(bytes.fromhex(data_bytes[2]), signed=False, byteorder='little')
        print(str(major) + "." + str(minor) + "." + str(patch))
        self.__exit(self.RETURN_CODES['success'])

    def get_hw_version(self):
        frame = self.__assemble_frame('get_hw_version', None)
        res = self.__send_to_device('get_hw_version', frame)
        if not self.__is_frame_valid(res):
            # todo: handle weird response
            print("err invalid data")
            pass
        data = self.__extract_data(res, join_str=':')
        data_bytes = data.split(':')
        major = int.from_bytes(bytes.fromhex(data_bytes[0]), signed=False, byteorder='little')
        minor = int.from_bytes(bytes.fromhex(data_bytes[1]), signed=False, byteorder='little')
        print(str(major) + "." + str(minor))
        self.__exit(self.RETURN_CODES['success'])

    def __decode_carel_configuration(self, data: str) -> str:
        """
        Decodes carel configuration data resulting in str of "<setpoint temp> <temp diff> <reverse diff> <neutral zone>"

        example: "19.0 1.0 2.0 1.5"

        :param data: unpacked values from frame
        """
        data: List[str] = data.split(":")
        resp: str = ""
        temp_values: List[str] = [b1 + b2 for b1, b2 in zip(data[::2], data[1::2])]
        for temp in temp_values:
            resp += str(int.from_bytes(bytes.fromhex(temp), signed=True, byteorder='little') / 10) + " "
        return resp.strip()

    def update_led_array(self, box: List[int], color: List[str]) -> None:
        """
        Updates state of LED array on box

        :param box: box number
        :param color: box color which should be used
        """
        pairs: List[Tuple[int, str]] = list(zip(box, color))
        data: int = 0

        for pair in pairs:
            for segment in self.BOX_MAPPING[pair[0]]:
                data |= (self.COLOR_MAPPING[pair[1]] << (segment * 2))

        # create binary string from computed data and reverse it
        bin_str: str = "{0:0{1}b}".format(data, self.BOX_LED_BYTES_LEN * 8)[::-1]

        # convert binary string to hex string
        hstr = '%0*X' % ((len(bin_str) + 3) // 4, int(bin_str, 2))

        # add module index to hex string
        hstr = '00' + hstr

        args = self.__split_str_to_bytes_list(hstr)

        frame = self.__assemble_frame('update_led_array', args)
        resp = self.__send_to_device('update_led_array', frame)

        if self.__is_frame_valid(resp):
            state = self.__extract_data(resp, '')
            print(state)
            self.__exit(self.RETURN_CODES['success'])
        else:
            print(f"ERR invalid frame detected! {resp.hex()}")
            self.__exit(self.RETURN_CODES['invalid_frame'])

    def leds_off(self):
        """
        Turn off LEDs on all boxes
        """
        frame = self.__assemble_frame('clear_led_array', ['00'])
        resp = self.__send_to_device('clear_led_array', frame)
        if not self.__is_frame_valid(resp):
            print(f"err invalid data: \"{resp}\"")
            self.__exit(self.RETURN_CODES['error_notification'])
        data = self.__extract_data(resp, '')
        print(data)
        self.__exit(self.RETURN_CODES['success'])

    def set_led_color(self, val_args: List[str]) -> None:
        """
        Set color variant for color index with rgb values

        | color_index: 0-3
        | r: 0-255
        | g: 0-255
        | b: 0-255

        example:
        ::
                  set_led_color 0 255 0 0
                  set_led_color 1 0 255 0
                  set_led_color 2 0 0 255
                  set_led_color 3 255 255 255

        :param val_args: [color_index, r, g, b]
        :return: None
        """
        # convert color index to hex
        color_val: int = self.COLOR_MAPPING[val_args[0].upper()]
        val_args[0] = '%0*X' % (2, color_val)
        # convert rgb values to hex
        for i in range(1, len(val_args)):
            val_args[i] = '%0*X' % (2, int(val_args[i]))
        # add module index to args
        val_args.insert(0, '00')  # add module index
        frame = self.__assemble_frame('set_led_color', val_args)
        resp = self.__send_to_device('set_led_color', frame)
        if not self.__is_frame_valid(resp):
            print(f"err invalid data: \"{resp.hex()}\"")
            self.__exit(self.RETURN_CODES['error_notification'])

        data = self.__extract_data(resp, '')
        print(data)
        self.__exit(self.RETURN_CODES['success'])

    def set_led_color_order(self, strip_index: int, val_arg: str) -> None:
        """
        Set color order for LED array

        :param strip_index: index of LED strip
        :param val_arg: RGB or RBG or GRB or GBR or BRG or BGR
        :return: None
        """
        val_args = val_arg.upper()
        if val_args not in self.BOX_LED_COLOR_ORDER:
            print(f"err invalid argument: \"{val_args}\"")
            self.__exit(self.RETURN_CODES['error_notification'])
        # get index of color order from list
        val_args = self.BOX_LED_COLOR_ORDER.index(val_args)
        # convert index to hex string
        val_args = hex(val_args)[2:].zfill(2)
        strip = hex(strip_index)[2:].zfill(2)
        frame = self.__assemble_frame('set_led_color_order', ['00', strip, val_args])
        resp = self.__send_to_device('set_led_color_order', frame)
        if not self.__is_frame_valid(resp):
            print(f"err invalid data: \"{resp.hex()}\"")
            self.__exit(self.RETURN_CODES['error_notification'])

        data = self.__extract_data(resp, '')
        print(data)
        self.__exit(self.RETURN_CODES['success'])

    def control_led_power(self, turn_on: bool) -> None:
        """
        Turn on/off led relay

        :param turn_on: True/False
        :return: None
        """
        if turn_on:
            val_args = ['00', '01']
        else:
            val_args = ['00', '00']
        frame = self.__assemble_frame('control_led_power', val_args)
        resp = self.__send_to_device('control_led_power', frame)
        if not self.__is_frame_valid(resp):
            print(f"err invalid data: \"{resp.hex()}\"")
            self.__exit(self.RETURN_CODES['error_notification'])

        data = self.__extract_data(resp, '')
        print(data)
        self.__exit(self.RETURN_CODES['success'])

    def set_led_power(self, val_arg: str) -> None:
        """
        Set power for LED array

        :param val_arg: ON or OFF
        :return: None
        """
        if val_arg not in ['ON', 'OFF']:
            print(f"err invalid argument: \"{val_arg}\"")
            self.__exit(self.RETURN_CODES['error_notification'])
        if val_arg == 'ON':
            self.control_led_power(True)
        else:
            self.control_led_power(False)

    def debug_led_binary(self, bin_str: str) -> None:
        split: List[str] = self.__split_str_to_bytes_list(bin_str)
        cnt: int = 1
        num: int = 0
        while num < len(split):
            if cnt not in [7, 14, 27, 34]:
                print("".join(split[num:num + 4]))
                print(split[num + 5])
                num += 5
                cnt += 1
            else:
                print("".join(split[num:num + 7]))
                print(split[num + 8])
                print(split[num + 9])
                print(split[num + 10])
                num += 10
                cnt += 1


def is_input_action_valid(action: str) -> bool:
    """
    verify if required action is available in definition
    """
    if action.casefold() in Box.ACTIONS.keys():
        return True

    if action.casefold().startswith("carel_get_temperature"):
        return True
    return False


def is_arg_value_ok(values) -> bool:
    """
    verify if argument is in correct form
    """
    for val in values:
        if len(val) != 2:
            try:
                float(val)
                return True
            except:
                return False
    return True


def print_all_actions():
    """
    print all available actions as a simple table
    """
    print('{:<30} {:<10}'.format('action', 'no. args'))
    for key in Box.ACTIONS:
        if key == "carel_get_temperature":
            for i in range(1, len(Box.CAREL_GET_TEMPERATURE_IDS) + 1):
                print('{:<30} {:<10}'.format(f'{key}_{i}', str(int.from_bytes(bytes.fromhex(Box.ACTIONS[key]['len']),
                                                                              byteorder='little'))))
            continue
        print('{:<30} {:<10}'.format(key,
                                     str(int.from_bytes(bytes.fromhex(Box.ACTIONS[key]['len']), byteorder='little'))))


def print_temp_sensors():
    sensors = ""
    for sensor_id in Box.CAREL_GET_TEMPERATURE_IDS:
        sensors += f" {sensor_id}"
    print(sensors.strip())


def is_monitoring_contact_action(action: str) -> bool:
    """
    verify if action is monitoring contact action
    """
    if action.casefold() in Box.MONITORING_CONTACT_ACTIONS:
        return True
    return False


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('-a', '--action', action='store',
                        help='Do specified action')
    parser.add_argument('-v', '--value', action='append',
                        help='Use this value within action')
    parser.add_argument('-d', '--device', action='store',
                        help='which serial device to use')
    parser.add_argument('--box', action='append', help='use with led control action valid numbers 1..N')
    parser.add_argument('--color', action='append', help='use with led control action valid values are A,B,C')
    parser.add_argument('--clear-leds', action='store_true', default=False, help='use to turn off all LED segments on '
                                                                                 'all boxes')
    parser.add_argument('--led-power', action='store', help='use to turn on/off led relay valid values are ON, OFF')
    parser.add_argument('--set-led-color-order', action='store', help='use to set led color order, valid values are: RGB, RBG, GRB, GBR, BRG, BGR prefix with strip number e.g. 1-RGB delimiter is -')
    parser.add_argument('--set-led-color', action='store', help='use to set led color preset, with color name A, B, C, NONE, suffix color values rgb e.g. A-255-255-255 delimiter is -')
    parser.add_argument('-p', '--print', action='store_true', help='print command which will be send to box')
    parser.add_argument('--version', action='store_true', help='get script version')
    parser.add_argument('--specification', action='store_true', help='get implemented specification version')
    parser.add_argument('--list-actions', action='store_true', help='print all possible actions')
    parser.add_argument('--list-temperature-sensors', action='store_true', default=False,
                        help='print all available temperature sensors')
    args = parser.parse_args()

    if args.version:
        print(VERSION)
        sys.exit(0)
    if args.specification:
        print(SPEC_VERSION)
        sys.exit(0)
    if args.list_actions:
        print_all_actions()
        sys.exit(0)
    if args.list_temperature_sensors:
        print_temp_sensors()
        sys.exit(0)
    if args.action is None:
        print('argument error action must be specified', file=sys.stderr)
        sys.exit(4)
    if args.device is None:  # or not os.path.exists(args.device):
        print('please specify device using -d or --device option', file=sys.stderr)
        sys.exit(1)
    box = Box(args.device, 19200)
    if is_input_action_valid(args.action):
        actn = args.action
        if args.action.startswith("carel_get_temperature"):
            actn = "carel_get_temperature"
        if args.print:
            print(box.get_action(actn.casefold(), args.value).hex())
            sys.exit(0)
        if args.set_led_color_order:
            color_order = args.set_led_color_order.split('-')
            if len(color_order) != 2:
                print('invalid color order format, please use RGB, RBG, GRB, GBR, BRG, BGR prefix with strip number e.g. 1-RGB delimiter is -', file=sys.stderr)
                sys.exit(1)
            box.set_led_color_order(int(color_order[0]), color_order[1])
            sys.exit(0)
        if args.set_led_color:
            color = args.set_led_color.split('-')
            if len(color) != 4:
                print('invalid color format, please use color name A, B, C, NONE, suffix color values rgb e.g. A-255-255-255 delimiter is -', file=sys.stderr)
                sys.exit(1)
            box.set_led_color(color)
            sys.exit(0)
        if args.led_power:
            if args.led_power.casefold() not in ['on', 'off']:
                print('invalid led power value, please use ON or OFF', file=sys.stderr)
                sys.exit(1)
            box.set_led_power(args.led_power)
            sys.exit(0)

        if args.value is None and box.ACTIONS[actn.casefold()]["len"] == "00":
            if actn == 'get_fw_version':
                box.get_fw_version()
            elif actn == 'get_hw_version':
                box.get_hw_version()
            else:
                box.do_action(args.action.casefold(), None)
        elif actn.casefold() == "update_led_array":
            if (args.color is None or args.box is None) and not args.clear_leds:
                print('invalid input, please specify box and color pairs or --clear-leds', file=sys.stderr)
                sys.exit(1)
            if args.clear_leds:
                box.leds_off()
                sys.exit(0)
            if len(args.color) != len(args.box):
                print(f"invalid count of colors({len(args.color)}) and boxes({len(args.box)}) please make pairs e.g. "
                      f"--box 1 --color A", file=sys.stderr)
                sys.exit(1)
            for c in args.color:
                if c not in box.COLORS:
                    print(f"ERROR found invalid color option: {c} please use one of {box.COLORS}")
                    sys.exit(1)
            for b in args.box:
                if int(b) not in box.BOX_MAPPING.keys():
                    print(f"ERROR found invalid box option: {b} please use one of {box.BOX_MAPPING.keys()}")
                    sys.exit(1)
            box_i: List[int] = []
            for b in args.box:
                box_i.append(int(b))
            box.update_led_array(box_i, args.color)
        elif is_monitoring_contact_action(actn.casefold()):
            box.monitoring_contact_information(actn.casefold(), ['00', '00'])
        else:
            if is_arg_value_ok(args.value):
                box.do_action(args.action.casefold(), args.value)
            else:
                print("invalid value given (expected 00-FF range)", file=sys.stderr)
                sys.exit(1)
    else:
        print("invalid action " + args.action, file=sys.stderr)
        sys.exit(4)
