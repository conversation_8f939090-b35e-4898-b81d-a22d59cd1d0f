#!/usr/bin/env python3
"""
Skript pro simulaci zaseknutí execute_command() po vyprintování "ano 1"
Nastaví podmínky tak, aby se funkce zasekla na await loop.run_in_executor()
"""

import asyncio
import sys
import os
import time

# Přidáme cestu k managers
sys.path.append(os.path.join(os.path.dirname(__file__), 'managers'))

from electronics_api import execute_command

def setup_hang_conditions():
    """
    Nasta<PERSON><PERSON> pod<PERSON>, kter<PERSON> způsob<PERSON> zaseknutí execute_command()
    """
    print("🔧 Nastavuji podmínky pro zaseknutí execute_command()...")
    
    def long_running_task(task_id):
        print(f"      🔄 Blokující task {task_id} běží...")
        time.sleep(300)  # 5 minut blokování
        return f"task_{task_id}_done"
    
    # Spustíme více tasků než je default pool size (obvykle 5)
    loop = asyncio.get_event_loop()
    
    for i in range(8):  # <PERSON><PERSON><PERSON> než default pool size
        future = loop.run_in_executor(None, long_running_task, i)
        # Nebudeme čekat na dokončení - necháme je běžet
    
    print("✅ Podmínky nastaveny - execute_command() by se měla zaseknout po 'ano 1'")

async def test_multiple_hangs(device="COM5", count=3):
    """
    Testuje více současných volání - všechny by se měly zaseknout
    """
    print(f"\n🔥 TEST VÍCE SOUČASNÝCH VOLÁNÍ ({count}x)")
    print("=" * 50)
    
    cmd = ["python", "boxctl-carel.py", "-d", device, "-a", "heart_beat"]
    
    tasks = []
    for i in range(count):
        task = asyncio.create_task(execute_command(cmd, f"hang_test_{i}"))
        tasks.append(task)
        print(f"🚀 Spuštěn task {i}")
    
    print(f"\n⏰ Čekám {count} sekund na výsledky...")
    
    try:
        results = await asyncio.wait_for(
            asyncio.gather(*tasks), 
            timeout=count + 2
        )
        
        print("❌ NEOČEKÁVANÝ VÝSLEDEK: Některé tasky se nezasekly!")
        successful = sum(1 for r in results if r[2] is None)
        print(f"   Dokončené: {successful}/{len(results)}")
        
    except asyncio.TimeoutError:
        print("✅ ÚSPĚCH: Všechny tasky se zasekly!")
        
        # Zkontrolujeme stav tasků
        completed = sum(1 for task in tasks if task.done())
        hanging = len(tasks) - completed
        
        print(f"   Dokončené: {completed}")
        print(f"   Zaseknuté: {hanging}")
        
        if hanging > 0:
            print("   🎯 Toto reprodukuje váš původní problém!")
        
        # Zrušíme zaseknuté tasky
        for task in tasks:
            if not task.done():
                task.cancel()

async def main():
    if len(sys.argv) < 2:
        print("Použití:")
        print("  python simulate_execute_hang.py <COM_PORT>")
        print("")
        print("Příklad:")
        print("  python simulate_execute_hang.py COM5")
        sys.exit(1)
    
    device = sys.argv[1]
    
    try:
        print("🎯 SIMULÁTOR ZASEKNUTÍ execute_command()")
        print("=" * 60)
        print(f"Zařízení: {device}")
        print()
        
        # Nastavíme podmínky pro zaseknutí
        setup_hang_conditions()
        
        # Počkáme chvilku, aby se podmínky ustálily
        await asyncio.sleep(2)
        
        await test_multiple_hangs(device, count=3)
        
        print(f"\n📋 SHRNUTÍ:")
        print("- Pokud jste viděli 'ano 1' ale ne 'ano 2', problém je reprodukován")
        print("- Příčina: ThreadPoolExecutor je vyčerpán/blokován")
        print("- Řešení: Použít vlastní executor nebo omezit současná volání")
        
    except KeyboardInterrupt:
        print("\n⚡ Test přerušen")
    except Exception as e:
        print(f"\n❌ Chyba: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"❌ Chyba v main: {e}")
        import traceback
        traceback.print_exc()
